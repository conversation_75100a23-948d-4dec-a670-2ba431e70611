# SmartTest API Endpoints

This document provides detailed information about the available API endpoints in the SmartTest API server.

## Authentication

Most endpoints require authentication. See the [Authentication](./authentication.md) document for details.

## Common Response Format

All API responses follow a consistent format:

```json
{
  "success": true|false,
  "message": "Human-readable message",
  "data": { ... } // Response data (if applicable)
}
```

## Error Responses

Error responses include additional information about the error:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information"
}
```

## API Endpoints

### Test Cases

#### Get Test Cases

```
GET /local/test-cases
```

Retrieves a list of test cases.

**Query Parameters:**

- `limit` (optional): Maximum number of test cases to return (default: 100)
- `suiteId` (optional): Filter test cases by suite ID
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "tc_id": "3180",
      "uid": "test_user",
      "status": "active",
      "case_driver": "selenium",
      "tp_id": 1,
      "comments": "Test case comments",
      "tickets": "JIRA-123",
      "name": "Login Test"
    },
    ...
  ],
  "message": "Test cases retrieved successfully"
}
```

#### Get Test Details

```
GET /local/test-details/:tsn_id
```

Retrieves details for a specific test run.

**Path Parameters:**

- `tsn_id`: Test session ID

**Query Parameters:**

- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "data": {
    "tsn_id": "12345",
    "tc_id": "3180",
    "uid": "test_user",
    "status": "running",
    "start_ts": "2023-01-01T12:00:00Z",
    "test_name": "Login Test",
    "description": "Test case description",
    "results": {
      "steps_total": 15,
      "steps_completed": 8,
      "steps_passed": 7,
      "steps_failed": 1
    }
  },
  "message": "Test details retrieved successfully"
}
```

### Test Suites

#### Get Test Suites

```
GET /local/test-suites
```

Retrieves a list of test suites.

**Query Parameters:**

- `limit` (optional): Maximum number of test suites to return (default: 100)
- `name` (optional): Filter test suites by name (partial match)
- `status` (optional): Filter test suites by status
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "ts_id": "101",
      "name": "Smoke Test Suite",
      "status": "active",
      "uid": "test_user",
      "tp_id": 201296,
      "comments": "Test suite comments"
    },
    ...
  ],
  "message": "Test suites retrieved successfully"
}
```

**Note:** The test suite structure has been updated to match the actual database schema. The fields `tcg_id`, `pj_id`, `tickets`, and `tag` are no longer available.

### Test Execution

#### Run Test Case

```
POST /api/case-runner
```

Executes a test case.

**Request Body:**

```json
{
  "tc_id": "3180",
  "uid": "test_user",
  "password": "password",
  "envir": "qa02",
  "shell_host": "jps-qa10-app01"
}
```

**Response:**

```json
{
  "success": true,
  "tsn_id": "12345",
  "message": "Test execution started successfully"
}
```

#### Run Test Suite

```
POST /api/case-runner
```

Executes a test suite.

**Request Body:**

```json
{
  "ts_id": "101",
  "uid": "test_user",
  "password": "password",
  "envir": "qa02",
  "shell_host": "jps-qa10-app01"
}
```

**Response:**

```json
{
  "success": true,
  "tsn_id": "12345",
  "message": "Test suite 101 started successfully with session ID 12345"
}
```

**Note:** The `/api/run-suite` endpoint is deprecated. Use `/api/case-runner` with `ts_id` parameter instead.

### Test Status

#### Get Test Status

```
GET /api/test-status
```

Retrieves the status of a test run. This endpoint uses cookie-based authentication with the external API.

**Query Parameters:**

- `tsn_id` (required): Test session ID
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "tsn_id": "12345",
  "status": "running",
  "progress": 50,
  "start_time": "2023-01-01 12:00:00",
  "end_time": null,
  "passed": 3,
  "failed": 0
}
```

**Note:** This endpoint uses the external `/AutoRun/ReportSummary` API on port 9080 with cookie-based authentication.

#### Stop Test

```
POST /api/stop-test
```

Stops a running test. This endpoint uses cookie-based authentication with the external API.

**Request Body:**

```json
{
  "tsn_id": "12345",
  "uid": "test_user",
  "password": "password"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Test session 12345 stopped successfully"
}
```

**Note:** This endpoint uses the external `/AutoRun/RemoveSession` API on port 9080 with cookie-based authentication.

### Recent Runs

#### Get Recent Runs

```
GET /local/recent-runs
```

Retrieves a list of recent test runs.

**Query Parameters:**

- `limit` (optional): Maximum number of runs to return (default: 10)
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "tsn_id": "12345",
      "start_time": "2023-01-01T12:00:00Z",
      "type": "TestCase",
      "envir": "qa02",
      "tc_id": "3180",
      "ts_id": null,
      "pj_id": null
    },
    ...
  ],
  "message": "Recent runs retrieved successfully"
}
```

### Active Tests

#### Get Active Tests

```
GET /local/active-tests
```

Retrieves a list of currently active tests.

**Query Parameters:**

- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "tsn_id": "12345",
      "tc_id": "3180",
      "initiator_user": "test_user",
      "creation_time": "2023-01-01T12:00:00Z",
      "is_current_user": true
    },
    ...
  ],
  "message": "Active tests retrieved successfully"
}
```

### Test Reports

#### Get Test Report Summary

```
GET /api/test-reports
```

Retrieves test reports.

**Query Parameters:**

- `limit` (optional): Maximum number of reports to return (default: 10)
- `time_range` (optional): Time range for reports (24h, 7d, 30d, custom)
- `start_date` (optional): Start date for custom time range
- `end_date` (optional): End date for custom time range
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "reports": [
    {
      "tsn_id": "12345",
      "test_id": "3180",
      "type": "Test Case",
      "environment": "QA02",
      "status": "passed",
      "start_time": "2023-01-01T12:00:00Z",
      "end_time": "2023-01-01T12:05:00Z",
      "duration": "5:00",
      "pass_rate": 100,
      "total_cases": 1,
      "passed_cases": 1,
      "failed_cases": 0,
      "uid": "test_user",
      "test_name": "Login Test"
    },
    ...
  ]
}
```

#### Get Test Report Details

```
GET /api/report-details
```

Retrieves detailed test report information. This endpoint uses cookie-based authentication with the external API.

**Query Parameters:**

- `tsn_id` (required): Test session ID
- `index` (optional): Page number for pagination (default: 1)
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "tsn_id": "12345",
  "steps": [
    {
      "tc_id": "3180",
      "seq_index": 1,
      "outcome": "P",
      "description": "Login to application",
      "input": "username=test, password=****",
      "output": "Login successful"
    },
    ...
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 3
  }
}
```

**Note:** This endpoint uses the external `/AutoRun/ReportDetails` API on port 9080 with cookie-based authentication.

### Test Sessions

#### Create Test Session

```
POST /AutoRun/TestSession
```

Creates a new test session.

**Request Body:**

```json
{
  "test_type": "TestCase",
  "environment": "qa02",
  "description": "Test session description",
  "uid": "test_user",
  "password": "password"
}
```

**Response:**

```json
{
  "success": true,
  "session_id": "12345",
  "message": "Test session created successfully"
}
```

#### Get Test Session

```
GET /AutoRun/TestSession/:id
```

Retrieves a specific test session.

**Path Parameters:**

- `id`: Test session ID

**Query Parameters:**

- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "session": {
    "session_id": "12345",
    "test_type": "TestCase",
    "environment": "qa02",
    "description": "Test session description",
    "status": "running",
    "created_by": "test_user",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:05:00Z"
  }
}
```

#### Get All Test Sessions

```
GET /AutoRun/TestSession
```

Retrieves all test sessions.

**Query Parameters:**

- `limit` (optional): Maximum number of sessions to return (default: 20)
- `offset` (optional): Offset for pagination (default: 0)
- `status` (optional): Filter by status
- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "sessions": [
    {
      "session_id": "12345",
      "test_type": "TestCase",
      "environment": "qa02",
      "description": "Test session description",
      "status": "running",
      "created_by": "test_user",
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:05:00Z"
    },
    ...
  ]
}
```

#### Update Test Session Status

```
POST /AutoRun/TestSession/:id/status
```

Updates the status of a test session.

**Path Parameters:**

- `id`: Test session ID

**Request Body:**

```json
{
  "status": "completed",
  "progress": 100,
  "uid": "test_user",
  "password": "password"
}
```

**Response:**

```json
{
  "success": true,
  "session_id": "12345",
  "status": "completed",
  "progress": 100
}
```

#### Get Test Session Report

```
GET /AutoRun/TestSession/:id/Report
```

Retrieves a report for a specific test session.

**Path Parameters:**

- `id`: Test session ID

**Query Parameters:**

- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "report": {
    "session_id": "12345",
    "test_type": "TestCase",
    "environment": "qa02",
    "description": "Test session description",
    "status": "completed",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:05:00Z",
    "duration": "5:00",
    "test_cases": [...],
    "input_queries": [...]
  }
}
```

### Input Queries

#### Log Input Query

```
POST /AutoRun/InputQuery
```

Logs a new input query.

**Request Body:**

```json
{
  "session_id": "12345",
  "query": "SELECT * FROM test_case",
  "execution_time": 100,
  "status": "success",
  "result": "Query result",
  "uid": "test_user",
  "password": "password"
}
```

**Response:**

```json
{
  "success": true,
  "query_id": "67890",
  "message": "Input query logged successfully"
}
```

#### Get Input Queries for Session

```
GET /AutoRun/InputQuery/:sessionId
```

Retrieves input queries for a specific session.

**Path Parameters:**

- `sessionId`: Test session ID

**Query Parameters:**

- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "queries": [
    {
      "query_id": "67890",
      "session_id": "12345",
      "query": "SELECT * FROM test_case",
      "execution_time": 100,
      "status": "success",
      "result": "Query result",
      "created_at": "2023-01-01T12:00:00Z"
    },
    ...
  ]
}
```

#### Get Input Query Stats for Session

```
GET /AutoRun/InputQuery/:sessionId/Stats
```

Retrieves input query statistics for a specific session.

**Path Parameters:**

- `sessionId`: Test session ID

**Query Parameters:**

- `uid` (required): User ID for authentication
- `password` (required): Password for authentication

**Response:**

```json
{
  "success": true,
  "stats": {
    "avg_execution_time": 100,
    "total_queries": 10,
    "success_rate": 90
  }
}
```

### Database Setup

#### Setup Database Schema

```
POST /AutoRun/setup
```

Sets up the database schema. This endpoint is restricted to admin users.

**Request Body:**

```json
{
  "uid": "<EMAIL>",
  "password": "password"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Database schema created successfully"
}
```
