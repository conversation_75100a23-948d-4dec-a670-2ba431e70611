/**
 * Config API Service - Unified Implementation
 *
 * Direct replacement of the original config API service
 * using the unified service with config context.
 */

// Import the unified service
import { UnifiedApiService } from '../../shared/services/unified-api-service.js';

// Create config-specific instance
const apiService = new UnifiedApiService();
apiService.moduleContext = 'config';
apiService.initializeConfiguration();

// Make it globally available (preserving existing interface)
window.apiService = apiService;

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = apiService;
}

console.log('Config API Service (Unified) initialized');