/**
 * Config API Service - Unified Implementation
 *
 * Direct replacement of the original config API service
 * using the unified service with config context.
 */

// Import the unified service
import { UnifiedApiService } from '../../shared/services/unified-api-service.js';

// Create config-specific instance
const apiService = new UnifiedApiService();
apiService.moduleContext = 'config';
apiService.initializeConfiguration();

<<<<<<< HEAD
// Make it globally available (preserving existing interface)
window.apiService = apiService;
=======
    // Setup API endpoints
    this.endpoints = {
      caseRunner: '/case-runner',         // -> /api/case-runner
      testStatus: '/test-status',         // -> /api/test-status
      testReport: '/test-report',         // -> /api/test-report
      testSuites: '/local/test-suites',   // -> /local/test-suites
      testCases: '/local/test-cases',     // -> /local/test-cases
      stopTest: '/stop-test',             // -> /api/stop-test
      rerunFailed: '/rerun-failed',       // -> /api/rerun-failed
      activeTests: '/local/active-tests', // -> /local/active-tests
      recentRuns: '/local/recent-runs',   // -> /local/recent-runs
      testDetailsEndpoint: '/local/test-details', 
      testReports: '/test-reports'        // -> /api/test-reports
    };

    // Default credentials
    this.credentials = { uid: '', password: '' };

    // Load credentials from session storage
    this.loadCredentials();

    // Debug log the endpoints
    console.log('API endpoints initialized:', this.endpoints);
  }

  /**
   * Set API credentials
   * @param {string} username - Username
   * @param {string} password - Password
   */
  setCredentials(username, password) {
    this.credentials = { uid: username, password: password };

    // Save to session storage
    sessionStorage.setItem('smarttest_uid', username);
    sessionStorage.setItem('smarttest_pwd', password);

    console.log('API credentials set for user:', username);
    return true;
  }

  /**
   * Load credentials from session storage or environment
   * @returns {boolean} - Whether credentials were successfully loaded
   */
  loadCredentials() {
    try {
      // Try to load from session storage first
      const uid = sessionStorage.getItem('smarttest_uid');
      const password = sessionStorage.getItem('smarttest_pwd');

      if (uid && password) {
        this.credentials = { uid, password };
        console.log(`Credentials loaded for user: ${uid}`);
        return true;
      }

      console.log('No valid credentials found, user needs to log in');
      return false;
    } catch (error) {
      console.error('Error loading credentials:', error);
      return false;
    }
  }

  /**
   * Get authentication parameters for API requests
   * @returns {Object} - Authentication parameters
   */
  getAuthParams() {
    return {
      uid: this.credentials.uid,
      password: this.credentials.password
    };
  }

  /**
   * Make a GET request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - Additional parameters
   * @returns {Promise<any>} - The response from the API
   */
  async getRequest(endpoint, params = {}) {
    let url;
    try {
      if (endpoint.startsWith('/local/')) {
        url = endpoint; // No /api prefix for local endpoints
      } else {
    url = this.baseUrl + endpoint;
  }

      console.log(`Making GET request to: ${url}`);

      // Add authentication parameters and any additional parameters
      const allParams = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Build query string
      const queryString = Object.entries(allParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

      // Make the request
      const response = await fetch(`${url}?${queryString}`);

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      const data = await response.json();

      return data;
    } catch (error) {
      console.error(`Error making GET request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Make a POST request to the API
   * @param {string} endpoint - The endpoint to call
   * @param {Object} params - The parameters to send
   * @returns {Promise<any>} - The response from the API
   */
  async postRequest(endpoint, params = {}) {
    try {
      // Build full URL with the correct server address
      const url = this.baseUrl + endpoint;

      console.log(`Making POST request to: ${url}`);

      // Add authentication parameters directly
      const requestData = {
        ...params,
        uid: this.credentials.uid,
        password: this.credentials.password
      };

      // Use URLSearchParams for proper form encoding
      const formData = new URLSearchParams();

      // Add all parameters to form data
      Object.entries(requestData).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Log request parameters for debugging (mask password)
      const logParams = {...requestData};
      if (logParams.password) logParams.password = '***';
      console.log('Request parameters:', logParams);

      // Make the request
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: formData
      });

      // Handle non-200 responses
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API error (${response.status}): ${errorText}`);
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse the response
      const data = await response.json();

      // Add success property for compatibility with frontend
      return { success: true, ...data };
    } catch (error) {
      console.error(`Error making POST request to ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Run a specific test case by ID
   * @param {string} tcId - Test case ID to run
   * @param {Object} params - Additional parameters to pass to the test case runner
   * @returns {Promise<Object>} - Response from the API
   */
  async runTestCase(tcId, params = {}) {
    console.log(`Running test case ${tcId} with params:`, params);
    console.log('Using endpoint:', this.endpoints.caseRunner);

    // Ensure we have the minimal required parameters
    const runParams = {
      tc_id: tcId,
      envir: params.envir || 'qa02',
      shell_host: params.shell_host || 'jps-qa10-app01',
      ...params // Any other params provided by the caller
    };

    try {
      // Call the CaseRunner API via our backend proxy
      const response = await this.postRequest(this.endpoints.caseRunner, runParams);
      console.log(`Test case ${tcId} run initiated:`, response);
      return response;
    } catch (error) {
      console.error(`Error running test case ${tcId}:`, error);
      throw error;
    }
  }

  /**
   * Run a test suite (sequential, single tsn_id)
   * @param {number} tsId - Test suite ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<string>} - Test suite run/session ID (tsn_id)
   */
  async runTestSuite(tsId, params = {}) {
    try {
      if (!tsId) {
        throw new Error('Test suite ID is required');
      }
      // Add required parameters for the test suite runner
      const testParams = {
        ts_id: tsId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...ApiService.DEFAULT_TEST_PARAMS,
        ...params
      };
      // Use the new /api/run-suite endpoint for sequential suite runs
      const response = await this.postRequest('/run-suite', testParams);
      if (response && response.tsn_id) {
        console.log(`Test suite ${tsId} running with session ID: ${response.tsn_id}`);
        return response.tsn_id;
      } else {
        throw new Error(response.message || `Failed to run test suite ${tsId}`);
      }
    } catch (error) {
      console.error(`Error running test suite ${tsId}:`, error);
      throw error;
    }
  }

  /**
   * Get test status
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test status data
   */
  async getTestStatus(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testStatus, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting test status for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get report summary for a test run
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<Object>} - Test report data
   */
  async getReportSummary(tsnId) {
    try {
      return await this.getRequest(this.endpoints.testReport, { tsn_id: tsnId });
    } catch (error) {
      console.error(`Error getting report for ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get available test suites
   * @returns {Promise<Array>} - List of test suites
   */
  async getTestSuites() {
    try {
      const response = await this.getRequest(this.endpoints.testSuites);
      return response.testSuites || [];
    } catch (error) {
      console.error('Error getting test suites:', error);
      throw error;
    }
  }

  /**
   * Get available test cases
   * @returns {Promise<Array>} - List of test cases
   */
  async getTestCases() {
    try {
      const response = await this.getRequest(this.endpoints.testCases);
      return response;
    } catch (error) {
      console.error('Error getting test cases:', error);
      throw error;
    }
  }

  /**
   * Stop a running test
   * @param {number} tsnId - Test suite run ID
   * @returns {Promise<boolean>} - Success status
   */
  async stopTest(tsnId) {
    try {
      const response = await this.postRequest(this.endpoints.stopTest, { tsn_id: tsnId });
      return response.success === true;
    } catch (error) {
      console.error(`Error stopping test ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Rerun failed tests from a previous test run
   * @param {number} tsnId - Original test suite run ID
   * @param {Object} params - Additional parameters
   * @returns {Promise<number>} - New test suite run ID
   */
  async rerunFailedTests(tsnId, params = {}) {
    try {
      const response = await this.postRequest(this.endpoints.rerunFailed, {
        tsn_id: tsnId,
        user_id: this.credentials.uid,
        username: this.credentials.uid,
        ...params
      });

      if (response && response.tsn_id) {
        return response.tsn_id;
      } else {
        throw new Error('Failed to get test suite run ID for rerun');
      }
    } catch (error) {
      console.error(`Error rerunning failed tests from ${tsnId}:`, error);
      throw error;
    }
  }

  /**
   * Get active tests
   * @returns {Promise<Array>} - List of active tests
   */
  async getActiveTests() {
    try {
      const response = await this.getRequest(this.endpoints.activeTests);
      return response.activeTests || [];
    } catch (error) {
      console.error('Error getting active tests:', error);
      throw error;
    }
  }

  /**
   * Get test results data for dashboard display
   * @returns {Promise<Object>} - Dashboard data
   */
  async getDashboardData() {
    try {
      // Query for active test data
      const activeTestsData = await this.getActiveTests();

      // Get recent test runs - we'll get this from SQL in real implementation
      // For now, construct from active tests data
      const recentRuns = activeTestsData.map(test => ({
        id: test.tsn_id,
        type: 'Test Case',
        environment: 'QA02',
        status: test.status || 'running',
        startTime: test.latest_activity || new Date().toISOString(),
        duration: 0
      }));

      // Count status totals
      const total = activeTestsData.length;
      const successful = activeTestsData.filter(test => test.outcome === 'P').length;
      const failed = activeTestsData.filter(test => test.outcome === 'F').length;
      const running = activeTestsData.filter(test => !test.outcome).length;

      return {
        summary: { total, successful, failed, running },
        recent: recentRuns,
        environment: 'QA02'
      };
    } catch (error) {
      console.error('Error getting dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get test reports for the reports page
   * @param {Object} params - Optional parameters like timeRange
   * @returns {Promise<Object>} - Standardized response object
   */
  async getTestReports(params = {}) {
    try {
      const rawResponse = await this.getRequest(this.endpoints.testReports, params);

      if (Array.isArray(rawResponse)) {
        // API returned a direct array, wrap it into the standard object structure
        let highestTsnId = null;
        if (rawResponse.length > 0) {
          // Attempt to find the highest tsn_id, assuming reports are somewhat ordered or tsn_id is numeric
          // A more robust solution would be if the API provides this or if reports are guaranteed sorted by tsn_id descending.
          const tsnIds = rawResponse.map(r => r.tsn_id).filter(id => id != null && !isNaN(Number(id)));
          if (tsnIds.length > 0) {
            highestTsnId = Math.max(...tsnIds.map(Number));
          }
        }
        return {
          success: true,
          reports: rawResponse,
          totalRecords: rawResponse.length, // This is the count of records in THIS response
          highestTsnIdInResponse: highestTsnId // The highest TSN ID found in THIS response
        };
      } else if (rawResponse && typeof rawResponse === 'object') {
        // API returned an object, check if it's already in the desired format or needs adaptation
        if (rawResponse.success === true) {
          // Already a success response, ensure all expected fields are present or defaulted
          return {
            success: true,
            reports: rawResponse.reports || rawResponse.newRuns || [],
            totalRecords: rawResponse.totalRecords || (rawResponse.reports || rawResponse.newRuns || []).length,
            highestTsnIdInResponse: rawResponse.highestTsnIdInResponse || rawResponse.latestTsnIdInDelta || null,
            // Include other properties if they exist, like 'message'
            ...(rawResponse.message && { message: rawResponse.message })
          };
        } else if (rawResponse.success === false) {
          // It's an error object from the API
          return rawResponse; // Pass it through
        } else if (rawResponse.hasOwnProperty('reports')) {
          // It's an object like { reports: [...] } but without a success flag - adapt it
          let highestTsnId = null;
          const reportsArray = rawResponse.reports || [];
          if (reportsArray.length > 0) {
            const tsnIds = reportsArray.map(r => r.tsn_id).filter(id => id != null && !isNaN(Number(id)));
            if (tsnIds.length > 0) {
              highestTsnId = Math.max(...tsnIds.map(Number));
            }
          }
          return {
            success: true, // Assume success if we have a reports array
            reports: reportsArray,
            totalRecords: rawResponse.totalRecords || reportsArray.length,
            highestTsnIdInResponse: rawResponse.highestTsnIdInResponse || highestTsnId
          };
        }
      }
      
      // If the response is not an array and not a recognized object structure, treat as an error/unexpected format
      console.error('Error getting test reports: Unexpected API response format.', rawResponse);
      return {
        success: false,
        message: 'Unexpected API response format.',
        reports: [],
        totalRecords: 0,
        highestTsnIdInResponse: null
      };

    } catch (error) {
      console.error('Error in ApiService.getTestReports:', error);
      // Ensure a standard error object structure is returned
      return {
        success: false,
        message: error.message || 'Failed to fetch test reports.',
        reports: [],
        totalRecords: 0,
        highestTsnIdInResponse: null,
        error: error // Optionally include the original error object
      };
    }
  }

  /**
   * Get test details
   * @param {string|number} tsnId - Test session ID
   * @returns {Promise<Object>} - API response with test details
   */
  async getTestDetails(tsnId) {
    try {
      console.log(`Getting test details for ${tsnId} using endpoint ${this.endpoints.testDetailsEndpoint}`);

      const response = await this.getRequest(`${this.endpoints.testDetailsEndpoint}/${tsnId}`);

      // Handle different response formats for backward compatibility
      if (response.success && response.test) {
        // Format from /local/test-details/:tsn_id endpoint
        return response.test;
      } else if (response.success && response.data) {
        // Generic success response format
        return response.data;
      } else if (response.test) {
        // Direct test object
        return response.test;
      }

      // Fallback to returning the whole response
      return response;
    } catch (error) {
      console.error(`Error getting test details for ${tsnId}:`, error);
      throw error;
    }
  }

  // Default test parameters to use with API requests
  static get DEFAULT_TEST_PARAMS() {
    return {
      environment: 'qa02',
      shell_host: 'jps-qa10-app01',
      file_path: '/home/<USER>/',
      operatorConfigs: 'operatorNameConfigs',
      kafka_server: 'kafka-qa-a0.lab.wagerworks.com',
      dataCenter: 'GU',
      rgs_env: 'qa02',
      old_version: '0',
      networkType1: 'multi-site',
      networkType2: 'multi-site',
      sign: '-',
      rate_src: 'local'
    };
  }
}

// Create global API service instance
window.apiService = new ApiService();
>>>>>>> 42dc3545ee6aede103947f6cca103f88ecd9a06b

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = apiService;
}

console.log('Config API Service (Unified) initialized');