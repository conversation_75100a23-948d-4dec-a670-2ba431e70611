/**
 * Dashboard API Service - Unified Implementation
 *
 * Direct replacement of the original dashboard API service
 * using the unified service with dashboard context.
 */

// Import the unified service
import { UnifiedApiService } from '../shared/services/unified-api-service.js';

// Create dashboard-specific instance
const apiService = new UnifiedApiService();
apiService.moduleContext = 'dashboard';
apiService.initializeConfiguration();

// Make it globally available (preserving existing interface)
window.apiService = apiService;

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = apiService;
}

console.log('Dashboard API Service (Unified) initialized');
