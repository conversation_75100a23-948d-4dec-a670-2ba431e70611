/**
 * Test Cases Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

// Get test cases
router.get('/test-cases', validateCredentials, async (req, res) => {
  try {
    console.log('GET /local/test-cases');
    // Use the database module to fetch test cases
    const testCases = await db.getTestCases(req.query);

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testCases || [],
      message: 'Test cases retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test cases:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test cases',
      error: error.message
    });
  }
});

// Get test details
router.get('/test-details/:tsn_id', validateCredentials, async (req, res) => {
  try {
    const { tsn_id } = req.params;
    console.log(`Getting test details for tsn_id: ${tsn_id}`);

    // Use the database module to fetch test details
    const testDetails = await db.getTestDetails(tsn_id);

    // Return as JSON with success flag
    return res.json({
      success: true,
      data: testDetails,
      message: 'Test details retrieved successfully'
    });
  } catch (error) {
    console.error('Error retrieving test details:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test details',
      error: error.message
    });
  }
});

module.exports = router;
