/**
 * Test Details Routes
 */
const express = require('express');
const router = express.Router();
const db = require('../database');
const { validateCredentials } = require('../middleware/auth');

/**
 * Get test details by tsn_id from the database
 */
router.get('/test-details/:tsn_id', validateCredentials, async (req, res) => {
  console.log('GET /local/test-details/:tsn_id');
  try {
    const { tsn_id } = req.params;
    
    if (!tsn_id) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameter: tsn_id'
      });
    }
    
    console.log(`Fetching test details for tsn_id: ${tsn_id}`);
    
    // Check if database is disabled
    if (process.env.DB_DISABLED === 'true') {
      console.log('Database is disabled, returning empty test details');
      return res.json({
        success: false,
        message: 'Test details not available - database disabled'
      });
    }
    
    try {
      // Get test details from the database using the high-level abstraction
      const test = await db.getTestSessionDetails(tsn_id);
      
      if (!test) {
        return res.status(404).json({
          success: false,
          message: `Test details not found for tsn_id: ${tsn_id}`
        });
      }
      
      console.log(`Retrieved test details for tsn_id: ${tsn_id}`);
      
      // Return the data in the expected format
      return res.json({
        success: true,
        test,
        message: 'Test details retrieved successfully'
      });
    } catch (dbError) {
      console.error('Database error:', dbError);
      // Provide a more helpful error message
      throw new Error(`Database access failed: ${dbError.message}`);
    }
  } catch (error) {
    console.error('Error in /local/test-details route:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to retrieve test details',
      error: error.message
    });
  }
});

module.exports = router;
