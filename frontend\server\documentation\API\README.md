# SmartTest API Documentation

This directory contains comprehensive documentation for the SmartTest API server.

## Table of Contents

1. [Architecture Overview](./architecture.md)
2. [API Endpoints](./endpoints.md)
3. [Authentication](./authentication.md)
4. [Database Schema](./database-schema.md)
5. [Error Handling](./error-handling.md)
6. [Proxy Functionality](./proxy.md)

## Quick Start

The SmartTest API server provides a RESTful interface for managing test sessions, executing test cases and suites, and retrieving test results. The API is organized into several logical groups:

- **Test Sessions**: Endpoints for creating and managing test sessions
- **Test Cases/Suites**: Endpoints for retrieving and executing test cases and suites
- **Test Results**: Endpoints for retrieving test results and reports
- **Active Tests**: Endpoints for monitoring currently running tests
- **Input Queries**: Endpoints for logging and retrieving input queries

All API endpoints require authentication using the credentials specified in the configuration.

## API Base URL

The API is accessible at the following base URL:

```
http://localhost:3000/
```

## Common Response Format

All API responses follow a consistent format:

```json
{
  "success": true|false,
  "message": "Human-readable message",
  "data": { ... } // Response data (if applicable)
}
```

## Error Responses

Error responses include additional information about the error:

```json
{
  "success": false,
  "message": "Error message",
  "error": "Detailed error information"
}
```

## Authentication

Most API endpoints require authentication. See the [Authentication](./authentication.md) document for details.

## Further Reading

For detailed information about specific aspects of the API, refer to the individual documentation files listed in the Table of Contents.
