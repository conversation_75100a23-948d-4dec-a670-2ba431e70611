# SmartTest API Architecture

This document provides an overview of the SmartTest API server architecture.

## Architectural Overview

The SmartTest API server is built using a modular architecture that separates concerns into distinct layers:

1. **API Layer**: Handles HTTP requests and responses
2. **Service Layer**: Contains business logic
3. **Data Access Layer**: Interacts with the database

## Directory Structure

The server code is organized into the following directory structure:

```
frontend/server/
├── api.js                  # Main entry point
├── config/                 # Configuration files
│   ├── app-config.js       # Application configuration
│   └── env-config.js       # Environment variables
├── middleware/             # Middleware functions
│   ├── auth.js             # Authentication middleware
│   ├── error-handler.js    # Error handling middleware
│   ├── logging.js          # Logging middleware
│   └── proxy.js            # Proxy middleware
├── routes/                 # Route handlers
│   ├── index.js            # Route registration
│   ├── test-cases.js       # Test case routes
│   ├── test-suites.js      # Test suite routes
│   ├── test-reports.js     # Report routes
│   ├── active-tests.js     # Active tests routes
│   ├── recent-runs.js      # Recent runs routes
│   ├── case-runner.js      # Case runner routes
│   ├── proxy-routes.js     # External API proxy routes
│   ├── test-sessions.js    # Test session routes
│   └── input-queries.js    # Input query routes
├── services/               # Business logic
│   ├── case-runner.js      # Case runner service
│   ├── cookie-auth.js      # Cookie authentication service
│   ├── stop-test.js        # Stop test service
│   ├── test-reports.js     # Report generation service
│   └── test-status.js      # Test status service
└── database/               # Database layer
    ├── index.js            # Main entry point
    ├── config/             # Database configuration
    ├── connections/        # Database connection implementations
    ├── queries/            # Pre-defined database queries
    └── utils/              # Database utilities
```

## Component Descriptions

### Main Entry Point (api.js)

The `api.js` file is the main entry point for the application. It:

- Creates the Express application
- Configures middleware
- Registers routes
- Initializes the database connection
- Starts the HTTP server

### Configuration (config/)

The `config/` directory contains configuration files:

- `app-config.js`: Application configuration (port, default parameters, etc.)
- `env-config.js`: Environment-specific configuration

### Middleware (middleware/)

The `middleware/` directory contains middleware functions:

- `auth.js`: Authentication middleware
- `error-handler.js`: Error handling middleware
- `logging.js`: Request logging middleware
- `proxy.js`: Proxy middleware for forwarding requests to external APIs

### Routes (routes/)

The `routes/` directory contains route handlers for different API endpoints:

- `index.js`: Registers all routes
- `test-cases.js`: Handles test case-related endpoints
- `test-suites.js`: Handles test suite-related endpoints
- `test-reports.js`: Handles test report-related endpoints
- `active-tests.js`: Handles active test-related endpoints
- `recent-runs.js`: Handles recent runs-related endpoints
- `test-details.js`: Handles test details-related endpoints
- `case-runner.js`: Handles test execution endpoints
- `proxy-routes.js`: Handles proxy routes to external APIs
- `test-sessions.js`: Handles test session-related endpoints
- `input-queries.js`: Handles input query-related endpoints

Each route file follows a consistent pattern:
1. Import required modules and middleware
2. Define route handlers that use the high-level database abstraction
3. Format responses in a standardized format
4. Export the router

### Services (services/)

The `services/` directory contains business logic:

- `case-runner.js`: Handles test case execution
- `cookie-auth.js`: Handles cookie-based authentication with external APIs
- `stop-test.js`: Handles stopping test runs
- `test-reports.js`: Handles test report generation
- `test-status.js`: Handles test status retrieval

### Database Layer (database/)

The `database/` directory contains the database layer components:

- `index.js`: Main entry point for database operations
- `config/`: Database configuration files
- `connections/`: Database connection implementations
- `queries/`: Pre-defined database queries
- `utils/`: Database utilities

## Request Flow

1. Client sends HTTP request to the server
2. Request passes through middleware (logging, authentication, etc.)
3. Request is routed to the appropriate route handler in the routes/ directory
4. Route handler calls the appropriate service or database method
5. Service performs business logic and interacts with the database layer
6. Database layer executes queries and returns results
7. Response is formatted in a standardized format and sent back to the client

### Client-Side Flow (Using ApiService)

1. Frontend component calls the ApiService with appropriate parameters
2. ApiService constructs the request URL and adds authentication parameters
3. ApiService makes the HTTP request to the server
4. ApiService processes the response and returns standardized data
5. Frontend component updates the UI with the received data

## Database Access

The SmartTest API server uses a modular database layer (`database/`) to handle database connections and operations. The database layer supports multiple connection methods:

- Direct SSH commands
- SSH tunnel

All database operations are performed via the database layer, which provides a consistent interface regardless of the underlying connection method. The database layer is designed to be extensible and maintainable, with separate modules for different types of queries.

## External API Integration

The SmartTest API server integrates with external APIs through three mechanisms:

1. **Proxy Middleware**: Forwards requests to external APIs on port 5080 and handles the responses.

2. **Cookie-Based Authentication Service**: For endpoints on port 9080 that require cookie-based authentication, the server uses a dedicated cookie authentication service to obtain and manage JSESSIONID cookies.

3. **Direct Client-Side Integration**: The reports page implements direct client-side integration with external APIs on port 9080, bypassing the server for improved performance and reliability.

## Error Handling

The SmartTest API server uses a centralized error handling mechanism. All errors are caught and processed by the error handling middleware, which returns appropriate error responses to the client.

## Authentication

The SmartTest API server uses two authentication mechanisms:

1. **Local Authentication**: A simple mechanism based on username and password. The authentication middleware validates the credentials and attaches the authenticated user to the request object.

2. **External Authentication**: For external APIs on port 9080, the server uses cookie-based authentication. The cookie authentication service logs in to the external API, obtains a JSESSIONID cookie, and uses it for subsequent requests.

## Logging

The SmartTest API server uses a logging middleware to log all requests. The logs include the request method, URL, IP address, and timestamp.
