/**
 * Reports API Service - Unified Implementation
 *
 * Direct replacement of the original reports API service
 * using the unified service with reports context.
 */

// Import the unified service
import { UnifiedApiService } from '../shared/services/unified-api-service.js';

// Create reports-specific instance
const apiService = new UnifiedApiService();
apiService.moduleContext = 'reports';
apiService.initializeConfiguration();

// Make it globally available (preserving existing interface)
window.apiService = apiService;

// Export for module usage (if needed)
if (typeof module !== 'undefined' && typeof module.exports !== 'undefined') {
  module.exports = apiService;
}

console.log('Reports API Service (Unified) initialized');
