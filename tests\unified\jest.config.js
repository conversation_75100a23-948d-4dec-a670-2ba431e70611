/**
 * Unified Jest Configuration for SmartTest Application
 * 
 * This configuration consolidates all testing requirements for the unified architecture:
 * - Unit tests for services and utilities
 * - Integration tests for API and database layers
 * - End-to-end tests for complete workflows
 * 
 * Migration from:
 * - frontend/reports/tests/
 * - frontend/server/tests/
 * - tests/ (root level)
 */

module.exports = {
  // Test environment
  testEnvironment: 'node',
  
  // Root directory for tests
  rootDir: '../../',
  
  // Test directories
  testMatch: [
    '<rootDir>/tests/unified/**/*.test.js',
    '<rootDir>/tests/unified/**/*.spec.js'
  ],
  
  // Module paths
  moduleDirectories: ['node_modules', '<rootDir>'],
  
  // Module name mapping for unified architecture
  moduleNameMapper: {
    '^@shared/(.*)$': '<rootDir>/frontend/shared/$1',
    '^@services/(.*)$': '<rootDir>/frontend/shared/services/$1',
    '^@utils/(.*)$': '<rootDir>/frontend/shared/utils/$1',
    '^@config/(.*)$': '<rootDir>/frontend/shared/config/$1',
    '^@mocks/(.*)$': '<rootDir>/tests/unified/mocks/$1',
    '^@test-utils/(.*)$': '<rootDir>/tests/unified/utils/$1'
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/tests/unified/setup.js'
  ],
  
  // Global teardown
  globalTeardown: '<rootDir>/tests/unified/teardown.js',
  
  // Coverage configuration
  collectCoverage: true,
  coverageDirectory: '<rootDir>/tests/unified/coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 85,
      lines: 85,
      statements: 85
    },
    // Specific thresholds for critical components
    './frontend/shared/services/': {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95
    }
  },
  
  // Files to collect coverage from
  collectCoverageFrom: [
    'frontend/shared/**/*.js',
    'frontend/dashboard/**/*.js',
    'frontend/reports/**/*.js',
    'frontend/config/**/*.js',
    'frontend/server/services/**/*.js',
    'frontend/server/database/**/*.js',
    '!**/node_modules/**',
    '!**/tests/**',
    '!**/coverage/**',
    '!**/*.config.js',
    '!**/*.setup.js'
  ],
  
  // Transform configuration
  transform: {
    '^.+\\.js$': 'babel-jest'
  },
  
  // Test timeout
  testTimeout: 30000,
  
  // Verbose output
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  
  // Restore mocks after each test
  restoreMocks: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Test result processor
  testResultsProcessor: '<rootDir>/tests/unified/utils/test-results-processor.js',
  
  // Reporters
  reporters: [
    'default',
    ['jest-html-reporters', {
      publicPath: '<rootDir>/tests/unified/coverage/html-report',
      filename: 'test-report.html',
      expand: true,
      hideIcon: false,
      pageTitle: 'SmartTest Unified Test Report'
    }]
  ],
  
  // Test categories using projects
  projects: [
    {
      displayName: 'Unit Tests',
      testMatch: ['<rootDir>/tests/unified/unit/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/unified/setup.js']
    },
    {
      displayName: 'Integration Tests',
      testMatch: ['<rootDir>/tests/unified/integration/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/unified/setup.js'],
      testTimeout: 60000
    },
    {
      displayName: 'E2E Tests',
      testMatch: ['<rootDir>/tests/unified/e2e/**/*.test.js'],
      setupFilesAfterEnv: ['<rootDir>/tests/unified/setup.js'],
      testTimeout: 120000
    }
  ],
  
  // Watch mode configuration
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
    '<rootDir>/tests/unified/coverage/'
  ],
  
  // Notification configuration
  notify: true,
  notifyMode: 'failure-change',
  
  // Bail configuration
  bail: false,
  
  // Max workers for parallel execution
  maxWorkers: '50%',
  
  // Cache configuration
  cache: true,
  cacheDirectory: '<rootDir>/tests/unified/.jest-cache'
};
