/**
 * Test Session Queries
 * Provides functions for querying test sessions
 */
const QueryBuilder = require('../utils/query-builder');
const formatter = require('../utils/result-formatter');

/**
 * Get active test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Array>} - Active test sessions
 */
async function getActiveTests(connection, filters = {}) {
  const { uid, limit = 20 } = filters;

  // Create query builder
  const queryBuilder = new QueryBuilder();

  // Base query
  queryBuilder.select('test_session s', [
    's.tsn_id',
    'COALESCE(s.tc_id, 0) as tc_id',
    's.uid as initiator_user',
    's.start_ts as creation_time'
  ]);

  // Filter for active tests (end_ts is NULL)
  queryBuilder.where('s.end_ts', 'IS', null);

  // Filter by user ID if provided
  if (uid) {
    queryBuilder.where('s.uid', '=', uid);
  }

  // Add ordering and limit
  queryBuilder.orderBy('s.start_ts', 'DESC');
  queryBuilder.limit(limit);

  // Build and execute query
  const { sql, params } = queryBuilder.build();

  try {
    const rows = await connection.query(sql, params);
    return formatter.formatActiveTests(rows, uid);
  } catch (error) {
    console.error('Error executing getActiveTests query:', error);

    // Try a simpler fallback query if the main query fails
    try {
      console.log('Attempting fallback query for active tests');
      const fallbackSql = `
        SELECT tsn_id, tc_id, uid, start_ts
        FROM test_session
        WHERE end_ts IS NULL
        ORDER BY start_ts DESC
        LIMIT ?
      `;
      const fallbackRows = await connection.query(fallbackSql, [limit]);
      return formatter.formatActiveTests(fallbackRows, uid);
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
}

/**
 * Get recent test sessions
 * @param {Object} connection - Database connection
 * @param {Object} filters - Optional filters
 * @returns {Promise<Object>} - Recent test sessions
 */
async function getRecentRuns(connection, filters = {}) {
  // If a time_range is specified (implying a full load for that range)
  // and no specific limit is given, use a very high default.
  // Otherwise, use the provided limit or a smaller default (e.g., for other uses of getRecentRuns).
  let effectiveLimit;
  if (filters.time_range && filters.limit === undefined) {
    effectiveLimit = 1000; // Default for full time-range loads (effectively "all")
    // console.log(`🔍 Database Query: No explicit limit for time_range, using default full load limit: ${effectiveLimit}`);
  } else if (filters.limit !== undefined) {
    const parsedLimit = parseInt(filters.limit, 10);
    effectiveLimit = parsedLimit === -1 ? 1000 : parsedLimit; // Interpret -1 as "all"
    // console.log(`🔍 Database Query: Explicit limit provided: ${filters.limit}, using: ${effectiveLimit}`);
  } else {
    effectiveLimit = 100; // Default for other cases (e.g., if called without time_range or limit by other services)
    // console.log(`🔍 Database Query: No time_range and no limit, using default: ${effectiveLimit}`);
  }

  try {
    console.log(`[GET_RECENT_RUNS] Initial filters:`, JSON.stringify(filters));
    
    let baseSql = `
      SELECT ts.tsn_id, ts.tc_id, ts.ts_id, ts.pj_id, ts.uid, 
             ts.start_ts, ts.end_ts, ts.error, 
             CASE 
               WHEN ts.tc_id IS NOT NULL THEN (SELECT name FROM test_case WHERE tc_id = ts.tc_id)
               WHEN ts.ts_id IS NOT NULL THEN (SELECT name FROM test_suite WHERE ts_id = ts.ts_id)
               ELSE NULL
             END AS test_name,
             CASE 
               WHEN ts.tc_id IS NOT NULL THEN 'Test Case'
               WHEN ts.ts_id IS NOT NULL THEN 'Test Suite'
               WHEN ts.pj_id IS NOT NULL THEN 'Project'
               ELSE 'Unknown'
             END AS type,
             ts.report
      FROM test_session ts
    `;
    
    const whereClauses = [];
    const queryParams = []; // For WHERE clause parameters

    let timeRangeFilterInput = filters.time_range;
    let parsedTimeRange = timeRangeFilterInput;

    if (typeof timeRangeFilterInput === 'string') {
        try {
            if (timeRangeFilterInput.startsWith('{') && timeRangeFilterInput.endsWith('}')) {
                parsedTimeRange = JSON.parse(timeRangeFilterInput);
            }
        } catch (e) {
            console.warn(`[GET_RECENT_RUNS] Time range string was not valid JSON: ${timeRangeFilterInput}`);
            parsedTimeRange = timeRangeFilterInput; // Keep original string if parsing failed
        }
    }
    console.log(`[GET_RECENT_RUNS] Parsed timeRangeFilter:`, parsedTimeRange);
    
    if (parsedTimeRange && parsedTimeRange !== 'all') {
        let startDate;
        let endDate; 
        const now = new Date();

        if (typeof parsedTimeRange === 'object' && parsedTimeRange.type === 'custom' && parsedTimeRange.start && parsedTimeRange.end) {
            startDate = new Date(parsedTimeRange.start);
            endDate = new Date(parsedTimeRange.end);
            endDate.setHours(23, 59, 59, 999); 
            console.log(`[GET_RECENT_RUNS] Custom range: Start=${startDate.toISOString()}, End=${endDate.toISOString()}`);
        } else if (typeof parsedTimeRange === 'string') {
            switch (parsedTimeRange) {
                case '1h': startDate = new Date(now.getTime() - 1 * 60 * 60 * 1000); break;
                case '24h': startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); break;
                case '7d': startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000); break;
                case '30d': startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); break;
                case '3m': startDate = new Date(new Date().setMonth(now.getMonth() - 3)); break;
                default: console.warn(`[GET_RECENT_RUNS] Unknown time range string: ${parsedTimeRange}`);
            }
            if(startDate) console.log(`[GET_RECENT_RUNS] Predefined range '${parsedTimeRange}': Start=${startDate.toISOString()}`);
        }

        if (startDate) {
            const formattedStartDate = startDate.toISOString().slice(0, 19).replace('T', ' ');
            whereClauses.push('ts.start_ts >= ?');
            queryParams.push(formattedStartDate);
        }
        if (endDate) { 
            const formattedEndDate = endDate.toISOString().slice(0, 19).replace('T', ' ');
            whereClauses.push('ts.start_ts <= ?');
            queryParams.push(formattedEndDate);
        }
        console.log(`[GET_RECENT_RUNS] WHERE clauses: ${whereClauses.join(' AND ')}, Query params for WHERE: ${JSON.stringify(queryParams)}`);
    } else {
         console.log(`[GET_RECENT_RUNS] No time range filter applied (all or undefined).`);
    }

    if (whereClauses.length > 0) {
        baseSql += ` WHERE ${whereClauses.join(' AND ')}`;
    }

    baseSql += ` ORDER BY ts.start_ts DESC, ts.tsn_id DESC LIMIT ?`;
    
    const finalSqlParams = [...queryParams, effectiveLimit];

    console.log(`[GET_RECENT_RUNS] Final SQL: ${baseSql}`);
    console.log(`[GET_RECENT_RUNS] Final Params: ${JSON.stringify(finalSqlParams)}`);
    
    const rows = await connection.query(baseSql, finalSqlParams);
    
    console.log(`✅ Database Query: Retrieved ${rows.length} test sessions`);
    
    // Log the first 3 full rows from the database for debugging
    console.log('📊 First 3 FULL rows from database:');
    for (let i = 0; i < Math.min(3, rows.length); i++) {
      console.log(rows[i]);
    }
    
    if (rows.length === 0) {
      console.log('No test sessions returned from database query');

      return [];
    }
    
    // IMPORTANT: Detect and correct data format issues
    rows.forEach(row => {
      // Fix NULL values that might be returned as strings
      if (row.start_ts === 'NULL') row.start_ts = null;
      if (row.end_ts === 'NULL') row.end_ts = null;
      if (row.uid === 'NULL') row.uid = null;
      if (row.tc_id === 'NULL') row.tc_id = null;
      if (row.ts_id === 'NULL') row.ts_id = null;
      
      // Check if dates contain email addresses (data corruption)
      if (row.end_ts && typeof row.end_ts === 'string' && row.end_ts.includes('@')) {
        console.error(`⚠️ DATA ERROR: end_ts contains an email address: ${row.end_ts}`);
        row.end_ts = null; // Reset to null as it's invalid
      }
      
      if (row.start_ts && typeof row.start_ts === 'string' && row.start_ts.includes('@')) {
        console.error(`⚠️ DATA ERROR: start_ts contains an email address: ${row.start_ts}`);
        row.start_ts = null; // Reset to null as it's invalid
      }

      // Set default environment since we can't get it from the database
      row.environment = 'qa02';
    });
    
    // Log a sample of the raw database data
    console.log(`📊 Sample RAW data from database (first row):`);
    console.log(JSON.stringify({
      tsn_id: rows[0].tsn_id,
      tc_id: rows[0].tc_id,
      uid: rows[0].uid,
      start_ts: rows[0].start_ts,
      end_ts: rows[0].end_ts,
      report_excerpt: rows[0].report ? rows[0].report.substring(0, 100) + '...' : null
    }, null, 2));
    
    // Process results in sequential batches to reduce SSH connection load
    const results = [];
    const batchSize = 5;
    const numBatches = Math.ceil(rows.length / batchSize);
    
    console.log(`⏳ Processing ${rows.length} sessions in ${numBatches} batches...`);
    
    // Create batches for processing
    for (let batchIndex = 0; batchIndex < numBatches; batchIndex++) {
      const batchStart = batchIndex * batchSize;
      const batchEnd = Math.min(batchStart + batchSize, rows.length);
      const batch = rows.slice(batchStart, batchEnd);
      
      // Process each session in the current batch
      for (const session of batch) {
        try {
          // Process the session data using the HTML report data
          const processedSession = processSessionData(session);
          results.push(processedSession);
        } catch (error) {
          console.error(`❌ Error processing session ${session.tsn_id}:`, error);
          
          // Add basic session data even if there was an error
          results.push(createBasicSessionData(session));
        }
      }
      
      // Add a small delay between batches to prevent SSH channel issues
      if (batchIndex < numBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }
    
    console.log(`✅ Processed ${results.length} sessions successfully`);
    
    // Log a sample of the processed data
    console.log(`📊 Sample PROCESSED data (first row):`);
    console.log(JSON.stringify(results[0], null, 2));
    
    const totalRecords = results.length;
    const highestTsnIdInResponse = results.length > 0 ? results[0].tsn_id : null;

    if (filters.since_tsn_id) {
      // This is an incremental fetch, db.getRecentRuns is expected to return newRuns and latestTsnIdInDelta
      // For now, we'll assume all results are new if since_tsn_id is present.
      // A more robust implementation would filter results based on since_tsn_id.
      return {
        newRuns: results,
        totalRecords, // This might represent the total in the new set, or overall total depending on query
        latestTsnIdInDelta: highestTsnIdInResponse // Assuming highestTsnId is the latest in this delta
      };
    } else {
      // This is a full fetch
      return {
        runs: results,
        totalRecords,
        highestTsnIdInResponse
      };
    }
  } catch (error) {
    console.error('Error in getRecentRuns:', error);
    // Return a structure that won't break the consuming service in case of error
    return {
      runs: [],
      newRuns: [],
      totalRecords: 0,
      highestTsnIdInResponse: null,
      latestTsnIdInDelta: null
    };
  }
}

/**
 * Process a session's data without additional database queries
 * @param {Object} session - The session data from database
 * @returns {Object} - Processed session data
 */
function processSessionData(session) {
  try {
    // Extract test name - use the one from the query if available
    let testName = session.test_name;
    let testType = session.type || 'Unknown';
    let passedCases = 0;
    let failedCases = 0;
    let environment = session.environment || 'qa02'; // Default
    let status = 'Unknown';
    
    if (session.report) {
      // Extract additional data from HTML report if available
      
      // Extract pass/fail counts
      const passMatch = session.report.match(/Case\(s\) passed: (\d+)/);
      const failMatch = session.report.match(/Case\(s\) failed: (\d+)/);
      
      if (passMatch && passMatch.length > 1) {
        passedCases = parseInt(passMatch[1], 10);
      }
      
      if (failMatch && failMatch.length > 1) {
        failedCases = parseInt(failMatch[1], 10);
      }
      
      // Check for fail/pass span to determine status
      if (session.report.includes("<span style='color:red'>FAIL</span>")) {
        status = 'Failed';
      } else if (session.report.includes("<span style='color:green'>PASS</span>")) {
        status = 'Passed';
      }
    }
    
    // If no test name was found, use fallbacks
    if (!testName) {
      if (session.tc_id) {
        testName = `Check Test Case ${session.tc_id}`;
      } else if (session.ts_id) {
        testName = `Test Suite ${session.ts_id}`;
      } else {
        testName = `Session ${session.tsn_id}`;
      }
    }
    
    // Determine test ID to display
    const displayId = session.tc_id || session.ts_id || session.pj_id || session.tsn_id;
    
    // Calculate duration if start_ts and end_ts are available
    let duration = null;
    if (session.start_ts && session.end_ts) {
      try {
        const start = new Date(session.start_ts);
        const end = new Date(session.end_ts);
        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          const durationMs = end - start;
          const minutes = Math.floor(durationMs / 60000);
          const seconds = Math.floor((durationMs % 60000) / 1000);
          duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
      } catch (e) {
        console.error('Error calculating duration:', e);
      }
    }
    
    // Determine status if not already set from report
    if (status === 'Unknown') {
      if (session.error === 'Quequed') {
        status = 'Queued';
      } else if (session.error === 'Running' || (!session.end_ts && session.start_ts)) {
        status = 'Running';
      } else if (session.end_ts) {
        if (failedCases > 0) {
          status = 'Failed';
        } else if (passedCases > 0) {
          status = 'Passed';
        } else {
          status = 'Completed';
        }
      }
    }
    
    // Calculate pass rate
    const totalCases = passedCases + failedCases;
    const passRate = totalCases > 0 
      ? Math.round((passedCases / totalCases) * 100) 
      : 0;
    
    // Format user display name from email
    let userDisplay = 'Unknown';
    if (session.uid) {
      const userId = session.uid;
      if (typeof userId === 'string' && userId.includes('@')) {
        userDisplay = userId.split('@')[0]; // Extract username from email
      } else if (userId) {
        userDisplay = String(userId);
      }
    }
    
    return {
      tsn_id: session.tsn_id,
      test_id: displayId,
      tc_id: session.tc_id,
      test_name: testName,
      type: testType,
      envir: environment,
      status: status,
      start_time: session.start_ts,
      end_time: session.end_ts,
      duration: duration,
      total_cases: totalCases,
      passed_cases: passedCases,
      failed_cases: failedCases,
      pass_rate: passRate,
      uid: session.uid,
      user_display: userDisplay  // Add formatted username for display
    };
  } catch (error) {
    console.error('Error in processSessionData:', error);
    return createBasicSessionData(session);
  }
}

/**
 * Create a basic session data object as fallback
 * @param {Object} session - The session data from database
 * @returns {Object} - Basic session data
 */
function createBasicSessionData(session) {
  const testType = session.type || (session.tc_id ? 'Test Case' : (session.ts_id ? 'Test Suite' : 'Project'));
  const displayId = session.tc_id || session.ts_id || session.pj_id || session.tsn_id;
  
  // Format user display name
  let userDisplay = 'Unknown';
  if (session.uid) {
    const userId = session.uid;
    if (typeof userId === 'string' && userId.includes('@')) {
      userDisplay = userId.split('@')[0];
    } else if (userId) {
      userDisplay = String(userId);
    }
  }
  
  return {
    tsn_id: session.tsn_id,
    test_id: displayId,
    tc_id: session.tc_id,
    test_name: session.test_name || `No name assigned (${testType} ${displayId})`,
    type: testType,
    envir: session.environment || 'qa02',
    status: session.error || (session.end_ts ? 'Completed' : 'Running'),
    start_time: session.start_ts,
    end_time: session.end_ts,
    duration: null,
    total_cases: 0,
    passed_cases: 0,
    failed_cases: 0,
    pass_rate: 0,
    uid: session.uid,
    user_display: userDisplay  // Add formatted username for display
  };
}

/**
 * Get test session details
 * @param {Object} connection - Database connection
 * @param {string|number} tsn_id - Test session ID
 * @returns {Promise<Object>} - Test session details
 */
async function getTestSessionDetails(connection, tsn_id) {
  try {
    // Get basic session information
    const sessionSql = `
      SELECT 
        ts.tsn_id,
        ts.tc_id,
        ts.ts_id,
        ts.pj_id,
        ts.uid,
        ts.start_ts,
        ts.end_ts,
        ts.error,
        ts.report
      FROM test_session ts
      WHERE ts.tsn_id = ?
    `;
    
    const sessionRows = await connection.query(sessionSql, [tsn_id]);
    
    if (sessionRows.length === 0) {
      throw new Error(`Test session with ID ${tsn_id} not found`);
    }
    
    const session = sessionRows[0];
    
    // Get test case results for this session
    const testCasesSql = `
      SELECT 
        tr.tc_id,
        tr.seq_index,
        tr.outcome,
        tr.creation_time,
        tr.cnt,
        tc.name as test_case_name,
        tc.comments as description
      FROM test_result tr
      LEFT JOIN test_case tc ON tr.tc_id = tc.tc_id
      WHERE tr.tsn_id = ?
      ORDER BY tr.seq_index
    `;
    
    const testCasesRows = await connection.query(testCasesSql, [tsn_id]);
    
    // Extract test case information from the report HTML if available
    let testCases = [];
    let passedCases = 0;
    let failedCases = 0;
    let testName = '';
    
    if (session.report) {
      // Extract test name from report HTML
      const suiteMatch = session.report.match(/Suite: .+?>(\d+)<\/a>.+?&nbsp;(.+?)</);
      const caseMatch = session.report.match(/Case: .+?>(\d+)<\/a>.+?&nbsp;(.+?)</);
      
      if (suiteMatch && suiteMatch.length > 2) {
        testName = suiteMatch[2];
      } else if (caseMatch && caseMatch.length > 2) {
        testName = caseMatch[2];
      }
      
      // Extract pass/fail counts
      const passMatch = session.report.match(/Case\(s\) passed: (\d+)/);
      const failMatch = session.report.match(/Case\(s\) failed: (\d+)/);
      
      if (passMatch && passMatch.length > 1) {
        passedCases = parseInt(passMatch[1], 10);
      }
      
      if (failMatch && failMatch.length > 1) {
        failedCases = parseInt(failMatch[1], 10);
      }
      
      // Extract individual test cases from the report HTML
      const caseRegex = /<li><span style='color:(green|red)'>(PASS|FAIL)<\/span> Case: <a href='[^']+\?tc_id=(\d+)'>[^<]+<\/a>\s*&nbsp;([^<]+)<\/li>/g;
      let match;
      
      while ((match = caseRegex.exec(session.report)) !== null) {
        const status = match[2] === 'PASS' ? 'Passed' : 'Failed';
        const tcId = match[3];
        const description = match[4];
        
        // Only add if not already in the list (from database)
        if (!testCases.some(tc => tc.tc_id === tcId)) {
          testCases.push({
            tc_id: tcId,
            status: status,
            description: description,
            source: 'report'
          });
        }
      }
    }
    
    // Combine test cases from database and report
    if (testCasesRows.length > 0) {
      // Process test cases from database
      const dbTestCases = await Promise.all(testCasesRows.map(async (testCase) => {
        try {
          // Get output log if available
          let outputText = '';
          let inputText = '';
          
          if (testCase.cnt) {
            const outputSql = `SELECT txt FROM output WHERE cnt = ?`;
            const outputRows = await connection.query(outputSql, [testCase.cnt]);
            if (outputRows.length > 0) {
              outputText = outputRows[0].txt || '';
            }
            
            const inputSql = `SELECT txt FROM input WHERE cnt = ?`;
            const inputRows = await connection.query(inputSql, [testCase.cnt]);
            if (inputRows.length > 0) {
              inputText = inputRows[0].txt || '';
            }
          }
          
          // Extract error message from output if outcome is 'F'
          let errorMessage = '';
          if (testCase.outcome === 'F' && outputText) {
            const errorMatch = outputText.match(/Error:(.+?)(?:\n|$)/);
            if (errorMatch) {
              errorMessage = errorMatch[1].trim();
            }
          }
          
          return {
            tc_id: testCase.tc_id,
            seq_index: testCase.seq_index,
            status: testCase.outcome === 'P' ? 'Passed' : (testCase.outcome === 'F' ? 'Failed' : 'Unknown'),
            description: testCase.description || testCase.test_case_name || `Test Case ${testCase.tc_id}`,
            input_output: `Input: ${inputText.substring(0, 100)}${inputText.length > 100 ? '...' : ''}\nOutput: ${outputText.substring(0, 100)}${outputText.length > 100 ? '...' : ''}`,
            error_message: errorMessage,
            timestamp: testCase.creation_time,
            source: 'database'
          };
        } catch (error) {
          console.error(`Error getting details for test case ${testCase.tc_id}:`, error);
          return {
            tc_id: testCase.tc_id,
            seq_index: testCase.seq_index,
            status: testCase.outcome === 'P' ? 'Passed' : (testCase.outcome === 'F' ? 'Failed' : 'Unknown'),
            description: testCase.description || testCase.test_case_name || `Test Case ${testCase.tc_id}`,
            error_message: 'Error retrieving details',
            source: 'database'
          };
        }
      }));
      
      // Merge database test cases with those from the report
      testCases = dbTestCases.concat(
        testCases.filter(reportCase => 
          !dbTestCases.some(dbCase => dbCase.tc_id === reportCase.tc_id)
        )
      );
    }
    
    // If we still don't have pass/fail counts, count them from the test cases
    if (passedCases === 0 && failedCases === 0 && testCases.length > 0) {
      passedCases = testCases.filter(tc => tc.status === 'Passed').length;
      failedCases = testCases.filter(tc => tc.status === 'Failed').length;
    }
    
    // Calculate duration if start_ts and end_ts are available
    let duration = null;
    if (session.start_ts && session.end_ts) {
      try {
        const start = new Date(session.start_ts);
        const end = new Date(session.end_ts);
        if (!isNaN(start.getTime()) && !isNaN(end.getTime())) {
          const durationMs = end - start;
          const minutes = Math.floor(durationMs / 60000);
          const seconds = Math.floor((durationMs % 60000) / 1000);
          duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
      } catch (e) {
        console.error('Error calculating duration:', e);
      }
    }
    
    // Determine overall status
    let status = 'Unknown';
    if (session.end_ts) {
      if (failedCases > 0) {
        status = 'Failed';
      } else if (passedCases > 0) {
        status = 'Success';
      } else {
        status = 'Completed';
      }
    } else {
      status = 'Running';
    }
    
    // If we couldn't extract a test name from the report, use a default based on the ID
    if (!testName) {
      if (session.tc_id) {
        testName = `TC-${session.tc_id}`;
      } else if (session.ts_id) {
        testName = `TS-${session.ts_id}`;
      } else if (session.pj_id) {
        testName = `PJ-${session.pj_id}`;
      } else {
        testName = `Session ${session.tsn_id}`;
      }
    }
    
    // Determine test type
    const type = session.tc_id 
      ? 'Test Case' 
      : (session.ts_id ? 'Test Suite' : 'Project');
    
    // Calculate pass rate
    const totalCases = passedCases + failedCases;
    const passRate = totalCases > 0 
      ? Math.round((passedCases / totalCases) * 100) 
      : 0;
    
    // Extract environment from report if available
    let environment = 'qa02'; // Default
    if (session.report) {
      const envirMatch = session.report.match(/envir=([^<\s]+)/);
      if (envirMatch && envirMatch.length > 1) {
        environment = envirMatch[1];
      }
    }
    
    // Build the complete session details object
    return {
      tsn_id: session.tsn_id,
      test_id: session.tc_id || session.ts_id || session.pj_id || '',
      test_name: testName,
      type: type,
      envir: environment,
      status: status,
      start_time: session.start_ts,
      end_time: session.end_ts,
      duration: duration,
      total_cases: totalCases,
      passed_cases: passedCases,
      failed_cases: failedCases,
      skipped_cases: 0, // Not tracked in this database
      pass_rate: passRate,
      uid: session.uid,
      test_cases: testCases,
      report_html: session.report // Include the original HTML for rendering if needed
    };
  } catch (error) {
    console.error(`Error getting test session details for ID ${tsn_id}:`, error);
    throw error;
  }
}

/**
 * Extract environment information from report HTML
 * @param {string} report - The report HTML content
 * @returns {string} - The environment value
 */
function extractEnvironment(report) {
  // Default fallback value
  let environment = 'qa02';
  
  if (!report) {
    return environment;
  }
  
  try {
    // First try the specific 'envir=' parameter which appears to be the canonical one
    const envirMatch = report.match(/[<,\s]envir=([^<\s,]+)/);
    if (envirMatch && envirMatch.length > 1) {
      return envirMatch[1];
    }
    
    // If that's not found, try the more generic 'environment=' parameter
    const environmentMatch = report.match(/[<,\s]environment=([^<\s,]+)/);
    if (environmentMatch && environmentMatch.length > 1) {
      return environmentMatch[1];
    }
  } catch (error) {
    console.error('❌ Error extracting environment information:', error);
  }
  
  return environment;
}

/**
 * Get test details by tsn_id
 * @param {Object} connection - Database connection
 * @param {Object} params - Parameters including tsn_id
 * @returns {Promise<Object>} - Test details
 */
async function getTestDetails(connection, params = {}) {
  const { tsn_id } = params;
  
  if (!tsn_id) {
    throw new Error('Missing required parameter: tsn_id');
  }
  
  console.log(`Getting test details for tsn_id: ${tsn_id}`);
  
  try {
    // Use the existing getTestSessionDetails function
    const sessionDetails = await getTestSessionDetails(connection, tsn_id);
    
    if (!sessionDetails) {
      console.log(`No test details found for tsn_id: ${tsn_id}`);
      return null;
    }
    
    // The session details already contain all the info we need
    return sessionDetails;
  } catch (error) {
    console.error(`Error getting test details for tsn_id ${tsn_id}:`, error);
    throw error;
  }
}

module.exports = {
  getActiveTests,
  getRecentRuns,
  getTestSessionDetails,
  getTestDetails
};
