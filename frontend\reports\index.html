<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Automation Reports</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <!-- DataTables FixedHeader CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.3.1/css/fixedHeader.bootstrap5.min.css">
    <!-- DataTables SearchPanes CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/searchpanes/2.2.0/css/searchPanes.bootstrap5.min.css">
    <!-- DataTables Select CSS (required by SearchPanes) -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/select/1.7.0/css/select.bootstrap5.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables FixedHeader JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.3.1/js/dataTables.fixedHeader.min.js"></script>
    <!-- DataTables SearchPanes JS -->
    <script type="text/javascript" src="https://cdn.datatables.net/searchpanes/2.2.0/js/dataTables.searchPanes.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/searchpanes/2.2.0/js/searchPanes.bootstrap5.min.js"></script>
    <!-- DataTables Select JS (required by SearchPanes) -->
    <script type="text/javascript" src="https://cdn.datatables.net/select/1.7.0/js/dataTables.select.min.js"></script>
</head>
<body>
    <header class="navbar navbar-dark sticky-top bg-dark flex-md-nowrap p-0 shadow">
        <a class="navbar-brand col-md-3 col-lg-2 me-0 px-3" href="#">Test Automation Framework</a>
        <button class="navbar-toggler position-absolute d-md-none collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-nav">
            <div class="nav-item text-nowrap">
                <span class="nav-link px-3" id="environment-display">Environment: Development</span>
            </div>
        </div>
    </header>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../dashboard/index.html">
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../config/index.html">
                                Configuration
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="#">
                                Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Test Reports</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="export-btn">Export</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="reset-btn">Reset</button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Last 30 Days
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="timeRangeDropdown">
                                <li><a class="dropdown-item" href="#" data-range="1h">Last Hour</a></li>
                                <li><a class="dropdown-item" href="#" data-range="24h">Last 24 Hours</a></li>
                                <li><a class="dropdown-item" href="#" data-range="7d">Last 7 Days</a></li>
                                <li><a class="dropdown-item active" href="#" data-range="30d">Last 30 Days</a></li>
                                <li><a class="dropdown-item" href="#" data-range="3m">Last 3 Months</a></li>
                                <li><a class="dropdown-item" href="#" data-range="all">All Dates</a></li>
                                <!-- Custom Range option removed but functionality preserved for potential future use -->
                            </ul>
                        </div>
                        <!-- Placeholder for Custom Date Range Inputs -->
                        <div id="custom-range-controls" class="ms-2" style="display: none;">
                            <input type="date" id="custom-start-date" class="form-control-sm">
                            <input type="date" id="custom-end-date" class="form-control-sm ms-1">
                            <button id="apply-custom-range" class="btn btn-sm btn-outline-primary ms-1">Apply</button>
                        </div>
                    </div>
                </div>

                <div id="test-details-section" class="d-none mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4 id="test-details-title">Test Details</h4>
                            <button type="button" class="btn-close" aria-label="Close" id="close-details-btn"></button>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="test-detail-item">
                                        <span class="detail-label">ID:</span>
                                        <span class="detail-value" id="detail-test-id"></span>
                                    </div>
                                    <div class="test-detail-item">
                                        <span class="detail-label">Type:</span>
                                        <span class="detail-value" id="detail-test-type"></span>
                                    </div>
                                    <div class="test-detail-item">
                                        <span class="detail-label">Environment:</span>
                                        <span class="detail-value" id="detail-environment"></span>
                                    </div>
                                    <div class="test-detail-item">
                                        <span class="detail-label">Start Time:</span>
                                        <span class="detail-value" id="detail-start-time"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="test-detail-item">
                                        <span class="detail-label">Status:</span>
                                        <span class="detail-value" id="detail-status"></span>
                                    </div>
                                    <div class="test-detail-item">
                                        <span class="detail-label">Duration:</span>
                                        <span class="detail-value" id="detail-duration"></span>
                                    </div>
                                    <div class="test-detail-item">
                                        <span class="detail-label">User:</span>
                                        <span class="detail-value" id="detail-user"></span>
                                    </div>
                                    <div class="test-detail-item">
                                        <span class="detail-label">Triggered By:</span>
                                        <span class="detail-value" id="detail-trigger"></span>
                                    </div>
                                </div>
                            </div>

                            <h5 class="mt-4 mb-3">Test Cases</h5>
                            <div class="table-responsive">
                                <table class="table table-sm table-hover" id="test-cases-table">
                                    <thead>
                                        <tr>
                                            <th scope="col">Case ID</th>
                                            <th scope="col">Name/Description</th>
                                            <th scope="col">Status</th>
                                            <th scope="col">Duration</th>
                                            <th scope="col">Error Message</th>
                                            <th scope="col">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="test-cases-table-body">
                                        <!-- Test case rows will be dynamically inserted here by reports.js -->
                                        <tr>
                                            <td colspan="6" class="text-center">Loading test cases...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <h5>Test Results</h5>
                            <div class="test-results-stats mb-3">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-title">Total Cases</div>
                                            <div class="stat-value" id="detail-total-cases">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card bg-success text-white">
                                            <div class="stat-title">Passed</div>
                                            <div class="stat-value" id="detail-passed-cases">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card bg-danger text-white">
                                            <div class="stat-title">Failed</div>
                                            <div class="stat-value" id="detail-failed-cases">0</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="stat-card bg-warning text-white">
                                            <div class="stat-title">Skipped</div>
                                            <div class="stat-value" id="detail-skipped-cases">0</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h5>Test Cases</h5>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Case ID</th>
                                            <th>Description</th>
                                            <th>Status</th>
                                            <th>Duration</th>
                                            <th>Error Message</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="test-cases-table">
                                        <!-- Test cases will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container mt-4">
                    <h1>Test Reports</h1>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Test Results</h5>
                                    <div class="summary-counts">
                                        <span id="total-count-label">Total: <span id="total-count">0</span></span> |
                                        <span id="passed-count-label" class="text-success">Passed: <span id="passed-count">0</span></span> |
                                        <span id="failed-count-label" class="text-danger">Failed: <span id="failed-count">0</span></span> |
                                        <span id="running-count-label" class="text-primary">Running/Queued: <span id="running-count">0</span></span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <button id="refresh-btn" class="btn btn-primary">Refresh Test Results</button>
                                        </div>

                                        <div class="timezone-selector d-flex align-items-center">
                                            <label for="timezone-select" class="me-2">Timezone:</label>
                                            <select id="timezone-select" class="form-select form-select-sm" style="width: auto;">
                                                <option value="local">Local</option>
                                                <option value="America/Los_Angeles">US Pacific</option>
                                                <option value="UTC">UTC</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Filter Controls -->
                                    <div class="filter-controls bg-light p-3 mb-3 rounded">
                                        <div class="row">
                                            <div class="col-md-4 mb-2">
                                                <label for="filter-user" class="form-label">Filter by User</label>
                                                <select id="filter-user" class="form-select">
                                                    <option value="">All Users</option>
                                                    <!-- User options will be populated dynamically -->
                                                </select>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <label for="filter-status" class="form-label">Filter by Status</label>
                                                <select id="filter-status" class="form-select">
                                                    <option value="">All Statuses</option>
                                                    <option value="passed">Passed</option>
                                                    <option value="failed">Failed</option>
                                                    <option value="running">Running</option>
                                                    <option value="queued">Queued</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mb-2">
                                                <label for="filter-test-id" class="form-label">Filter by Test ID</label>
                                                <input type="text" id="filter-test-id" class="form-control" placeholder="Enter Test ID">
                                            </div>
                                        </div>
                                        <!-- Reset button row -->
                                        <div class="row mt-2">
                                            <div class="col-12 text-end">
                                                <button id="reset-filters" class="btn btn-secondary btn-sm">Reset All Filters</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Recent Test Results</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive" style="overflow-x: visible; overflow-y: visible;">
                                    <table class="table table-striped table-hover" id="reports-table">
                                        <thead>
                                            <tr>
                                                <th scope="col">ID</th>
                                                <th scope="col">Test Name</th>
                                                <th scope="col">Test ID</th>
                                                <th scope="col">Status</th>
                                                <th scope="col">Started</th>
                                                <th scope="col">Finished</th>
                                                <th scope="col">Duration</th>
                                                <th scope="col">Started By</th>
                                                <th scope="col">Passed</th>
                                                <th scope="col">Failed</th>
                                                <th scope="col">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Report data will be inserted here dynamically -->
                                            <tr>
                                                <td colspan="11" class="text-center">Loading data...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div id="loading-indicator" class="text-center p-3" style="display: none;">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <div id="refresh-status" class="text-muted mt-2 text-end"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Rest of the page content -->
                </div>

                <h3 class="mt-4">Test Performance Trends</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4>Success Rate by Test Type</h4>
                            </div>
                            <div class="card-body">
                                <canvas id="success-rate-chart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h4>Average Test Duration</h4>
                            </div>
                            <div class="card-body">
                                <canvas id="duration-trend-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Load unified API Service as ES6 module -->
    <script type="module" src="api-service.js"></script>
    <!-- Load shared enhanced external API service -->
    <script src="../shared/services/external-api-service.js"></script>
    <!-- Reports specific scripts -->
    <script src="reports.js"></script>
</body>
</html>