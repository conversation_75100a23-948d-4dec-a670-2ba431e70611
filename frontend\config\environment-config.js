/**
 * SmartTest Environment Configuration
 * 
 * This file provides environment-specific configurations for the SmartTest application.
 * It allows switching between environments (development, qa01, qa02, qa03, production)
 * and centralizes configuration settings for easier management.
 */

const environments = {
  development: {
    name: 'Development',
    apiBaseUrl: 'http://localhost:3000/api', // For local development
    autoRunBaseUrl: 'http://localhost:3000/AutoRun/',
    dbSettings: {
      host: 'localhost',
      port: 3306,
      user: 'dev_user',
      password: 'dev_password',
      database: 'smarttest'
    },
    mock: true // Use mock API for development by default
  },
  qa01: {
    name: 'QA01',
    apiBaseUrl: 'http://mprts-qa01.lab.wagerworks.com:9080/api',
    autoRunBaseUrl: 'http://mprts-qa01.lab.wagerworks.com:9080/AutoRun/',
    dbSettings: {
      host: 'mprts-qa01.lab.wagerworks.com',
      port: 3306,
      user: 'rgs_rw',
      password: 'rgs_rw',
      database: 'rgs_test'
    },
    mock: false
  },
  qa02: {
    name: 'QA02',
    apiBaseUrl: 'http://mprts-qa02.lab.wagerworks.com:5080/api',
    autoRunBaseUrl: 'http://mprts-qa02.lab.wagerworks.com:5080/AutoRun/',
    dbSettings: {
      host: 'mprts-qa02.lab.wagerworks.com',
      port: 3306,
      user: 'rgs_rw',
      password: 'rgs_rw',
      database: 'rgs_test'
    },
    mock: false
  },
  qa03: {
    name: 'QA03',
    apiBaseUrl: 'http://mprts-qa03.lab.wagerworks.com:5080/api',
    autoRunBaseUrl: 'http://mprts-qa03.lab.wagerworks.com:5080/AutoRun/',
    dbSettings: {
      host: 'mprts-qa03.lab.wagerworks.com',
      port: 3306,
      user: 'rgs_rw',
      password: 'rgs_rw',
      database: 'rgs_test'
    },
    mock: false
  },
  production: {
    name: 'Production',
    apiBaseUrl: 'https://mprts-prod.lab.wagerworks.com/api',
    autoRunBaseUrl: 'https://mprts-prod.lab.wagerworks.com/AutoRun/',
    dbSettings: {
      host: 'mprts-prod.lab.wagerworks.com',
      port: 3306,
      user: 'rgs_ro', // Read-only for production as a safety measure
      password: 'rgs_ro',
      database: 'rgs_test'
    },
    mock: false
  }
};

class EnvironmentConfig {
  constructor() {
    // Default to qa02 environment for compatibility with existing code
    this.currentEnv = 'qa02';
    
    // Try to load from localStorage if available
    this.loadFromStorage();
  }
  
  /**
   * Load environment setting from localStorage
   */
  loadFromStorage() {
    if (typeof localStorage !== 'undefined') {
      const storedEnv = localStorage.getItem('smarttest_environment');
      if (storedEnv && environments[storedEnv]) {
        this.currentEnv = storedEnv;
      }
    }
  }
  
  /**
   * Save environment setting to localStorage
   */
  saveToStorage() {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('smarttest_environment', this.currentEnv);
    }
  }
  
  /**
   * Get current environment configuration
   * @returns {Object} Environment configuration
   */
  get current() {
    return environments[this.currentEnv];
  }
  
  /**
   * Get list of available environments
   * @returns {Array} List of environment keys
   */
  get availableEnvironments() {
    return Object.keys(environments);
  }
  
  /**
   * Set current environment
   * @param {string} envKey - Environment key
   * @returns {boolean} Success status
   */
  setEnvironment(envKey) {
    if (environments[envKey]) {
      this.currentEnv = envKey;
      this.saveToStorage();
      return true;
    }
    return false;
  }
  
  /**
   * Check if mock API should be used
   * @returns {boolean} True if mock API should be used
   */
  get useMock() {
    return this.current.mock;
  }
  
  /**
   * Toggle mock API usage for current environment
   * @param {boolean} useMode - Whether to use mock API
   */
  setUseMock(useMock) {
    environments[this.currentEnv].mock = !!useMock;
  }
}

// Create and export singleton instance
const envConfig = new EnvironmentConfig();
export default envConfig;

// Make available globally
if (typeof window !== 'undefined') {
  window.envConfig = envConfig;
}
